// Load games from JSON and create cards
async function loadGames() {
    try {
        const response = await fetch('./list-game.json');
        const games = await response.json();
        const container = document.getElementById('gamesContainer');
        
        games.forEach(game => {
            const gameCard = createGameCard(game);
            container.appendChild(gameCard);
        });
    } catch (error) {
        console.error('Error loading games:', error);
    }
}

function createGameCard(game) {
    const card = document.createElement('div');
    card.className = 'card game-one-half-slot slots-games';
    card.setAttribute('data-rtp', game.rtp);
    card.setAttribute('data-name', game.name.toLowerCase());
    
    // Determine RTP color based on percentage
    let rtpColor = '#ff6b35'; // default red/orange
    if (game.rtp >= 75) {
        rtpColor = '#4ade80'; // green for high RTP
    } else if (game.rtp >= 60) {
        rtpColor = '#fbbf24'; // yellow for medium RTP
    }
    
    // Create pola HTML
    let polaHTML = '';
    if (typeof game.pola === 'string' && game.pola === 'unavailable') {
        polaHTML = `<div class="pola-unavailable">Pola tidak tersedia!<br>Silahkan gunakan pola anda pribadi.</div>`;
    } else if (Array.isArray(game.pola)) {
        polaHTML = game.pola.map(p => {
            const icons = p.pattern.map(status => 
                `<i class="fa ${status === 'check' ? 'fa-check' : 'fa-times'}"></i>`
            ).join(' ');
            return `
                <div class="pattern-row">
                    <span class="pattern-value">${p.value}</span>
                    <span class="pattern-icons">${icons}</span>
                    <span class="pattern-type">${p.type}</span>
                </div>
            `;
        }).join('');
    }
    
    card.innerHTML = `
        <div class="game-image-wrapper">
            <img src="${game.image}" alt="${game.name}" class="game-image" loading="lazy">
            <div class="play-overlay">
                <button class="play-btn">PLAY NOW</button>
            </div>
        </div>
        <div class="pbar">
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${game.rtp}%; background-color: ${rtpColor};"></div>
            </div>
            <span class="rtp-text">${game.rtp}%</span>
        </div>
        <div class="my-icon">
            <div class="pattern-section">
                ${polaHTML}
            </div>
        </div>
    `;
    
    return card;
}

// DOM Content Loaded
document.addEventListener("DOMContentLoaded", function () {
  loadGames();
  initializeAnimations();
  setupEventListeners();
  setupScrollAnimations();
  initializeProgressBars(); // Initialize progress bars first
  
  // Wait for games to load before checking RTP
  setTimeout(() => {
    checkAndUpdateRTP();
  }, 500);
  
  setupFilterListeners();
});

// Setup filter event listeners
function setupFilterListeners() {
    const searchInput = document.getElementById('searchGame');
    const rtpFilter = document.getElementById('rtpFilter');
    const providerFilter = document.getElementById('providerFilter');
    
    if (searchInput) {
        searchInput.addEventListener('input', filterGames);
    }
    
    if (rtpFilter) {
        rtpFilter.addEventListener('change', filterGames);
    }
    
    if (providerFilter) {
        providerFilter.addEventListener('change', filterGames);
    }
}

// Filter games based on search and filter criteria
function filterGames() {
    const searchTerm = document.getElementById('searchGame')?.value.toLowerCase() || '';
    const rtpFilter = document.getElementById('rtpFilter')?.value || 'all';
    const providerFilter = document.getElementById('providerFilter')?.value || 'all';
    
    const gameCards = document.querySelectorAll('.card.game-one-half-slot');
    
    gameCards.forEach(card => {
        const gameName = card.getAttribute('data-name') || '';
        const gameRtp = parseInt(card.getAttribute('data-rtp')) || 0;
        
        let showCard = true;
        
        // Filter by search term
        if (searchTerm && !gameName.includes(searchTerm)) {
            showCard = false;
        }
        
        // Filter by RTP range
        if (rtpFilter !== 'all') {
            switch (rtpFilter) {
                case 'high':
                    if (gameRtp < 75) showCard = false;
                    break;
                case 'medium':
                    if (gameRtp < 60 || gameRtp >= 75) showCard = false;
                    break;
                case 'low':
                    if (gameRtp >= 60) showCard = false;
                    break;
            }
        }
        
        // Show/hide card
        card.style.display = showCard ? 'block' : 'none';
    });
}

console.log('RTP Slot website loaded');

// Initialize progress bars with their data-rtp values
function initializeProgressBars() {
  const progressFills = document.querySelectorAll(".rtp-progress-fill");

  progressFills.forEach((fill) => {
    const rtpValue = fill.getAttribute("data-rtp");
    if (rtpValue) {
      fill.style.width = rtpValue + "%";
    }
  });
}

// Initialize page animations
function initializeAnimations() {
  // Add staggered animation to game cards
  const gameCards = document.querySelectorAll(".game-card");
  gameCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`;
  });

  // Add floating animation to provider logos
  const providers = document.querySelectorAll(".provider-item");
  providers.forEach((provider, index) => {
    provider.style.animationDelay = `${index * 0.2}s`;
    provider.classList.add("float-animation");
  });
}

// Setup event listeners
function setupEventListeners() {
  // Header buttons
  const buktiBtn = document.querySelector(".btn-primary");
  const prediksiBtn = document.querySelector(".btn-secondary");

  buktiBtn.addEventListener("click", () => {
    showNotification("Fitur Bukti Bayar akan segera hadir!", "info");
  });

  prediksiBtn.addEventListener("click", () => {
    showNotification(
      "Halaman Prediksi Togel sedang dalam pengembangan!",
      "info"
    );
  });

  // Play buttons
  const playButtons = document.querySelectorAll(".play-btn");
  playButtons.forEach((btn) => {
    btn.addEventListener("click", function (e) {
      const gameCard = e.target.closest(".game-card");
      const gameImg = gameCard.querySelector(".game-image img");
      const gameName = gameImg.alt;

      // Add click animation
      btn.style.transform = "scale(0.95)";
      setTimeout(() => {
        btn.style.transform = "";
      }, 150);

      showNotification(`Launching ${gameName}...`, "success");

      // Simulate loading
      btn.textContent = "LOADING...";
      btn.disabled = true;

      setTimeout(() => {
        btn.textContent = "PLAY NOW";
        btn.disabled = false;
      }, 2000);
    });
  });

  // Provider items
  const providerItems = document.querySelectorAll(".provider-item");
  providerItems.forEach((item) => {
    item.addEventListener("click", function () {
      const providerImg = item.querySelector("img");
      const providerName = providerImg.alt;
      showNotification(`Filtering games by ${providerName}...`, "info");

      // Add selection effect
      providerItems.forEach((p) => p.classList.remove("selected"));
      item.classList.add("selected");

      // Filter games simulation
      filterGamesByProvider(providerName);
    });
  });

  // Game cards hover effects
  const gameCards = document.querySelectorAll(".game-card");
  gameCards.forEach((card) => {
    card.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-8px) scale(1.02)";
    });

    card.addEventListener("mouseleave", function () {
      this.style.transform = "";
    });
  });
}

// Setup scroll animations
function setupScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("animate-in");
      }
    });
  }, observerOptions);

  // Observe sections
  const sections = document.querySelectorAll(
    ".section-title, .providers-section, .game-card"
  );
  sections.forEach((section) => {
    observer.observe(section);
  });
}

// Add random RTP updates simulation
function addRandomRTPUpdates() {
  // Initialize RTP updates - every 1 hour (3600000ms)
  setInterval(updateRTPValues, 3600000); // Update every 1 hour
  updateRTPValues(); // Initial update

  // Check and update RTP on page load
  checkAndUpdateRTP();
}

// Update RTP values with range 48%-98%
function updateRTPValues() {
  const progressFills = document.querySelectorAll(".progress-fill");
  const percentageSpans = document.querySelectorAll(".rtp-text");
  const rtpValues = [];

  progressFills.forEach((fill, index) => {
    // Generate random RTP between 48% and 98%
    const newRTP = Math.floor(Math.random() * (98 - 48 + 1)) + 48;
    
    // Determine RTP color based on percentage
    let rtpColor = '#ff6b35'; // default red/orange
    if (newRTP >= 75) {
        rtpColor = '#4ade80'; // green for high RTP
    } else if (newRTP >= 60) {
        rtpColor = '#fbbf24'; // yellow for medium RTP
    }

    // Add pulse animation to container
    const container = fill.closest(".pbar");
    if (container) {
      container.style.animation = "pulse 0.5s ease-in-out";
    }

    setTimeout(() => {
      // Update progress bar width, color and data attribute
      fill.style.width = newRTP + "%";
      fill.style.backgroundColor = rtpColor;
      fill.setAttribute("data-rtp", newRTP);
      
      // Update card data attribute
      const card = fill.closest('.card');
      if (card) {
        card.setAttribute('data-rtp', newRTP);
      }

      // Update percentage text
      if (percentageSpans[index]) {
        percentageSpans[index].textContent = `${newRTP}%`;
      }

      if (container) {
        container.style.animation = "";
      }
    }, 250);

    rtpValues.push(newRTP);
  });

  // Save RTP values and update time
  saveRTPValues(rtpValues);
  localStorage.setItem("lastRTPUpdate", Date.now().toString());
}

// Store last update time in localStorage
function checkAndUpdateRTP() {
  const lastUpdate = localStorage.getItem("lastRTPUpdate");
  const now = Date.now();
  const oneHour = 3600000; // 1 hour in milliseconds

  if (!lastUpdate || now - parseInt(lastUpdate) >= oneHour) {
    updateRTPValues();
  } else {
    // Load saved RTP values if within 1 hour
    loadSavedRTPValues();
  }
}

// Load saved RTP values from localStorage
function loadSavedRTPValues() {
  const savedRTPs = localStorage.getItem("currentRTPValues");
  if (savedRTPs) {
    const rtpData = JSON.parse(savedRTPs);
    const progressFills = document.querySelectorAll(".progress-fill");
    const percentageSpans = document.querySelectorAll(".rtp-text");

    rtpData.forEach((rtp, index) => {
      if (progressFills[index] && percentageSpans[index]) {
        // Determine RTP color based on percentage
        let rtpColor = '#ff6b35'; // default red/orange
        if (rtp >= 75) {
            rtpColor = '#4ade80'; // green for high RTP
        } else if (rtp >= 60) {
            rtpColor = '#fbbf24'; // yellow for medium RTP
        }
        
        progressFills[index].setAttribute("data-rtp", rtp);
        progressFills[index].style.width = rtp + "%";
        progressFills[index].style.backgroundColor = rtpColor;
        percentageSpans[index].textContent = rtp + "%";
        
        // Update card data attribute
        const card = progressFills[index].closest('.card');
        if (card) {
          card.setAttribute('data-rtp', rtp);
        }
      }
    });
  }
}

// Save current RTP values to localStorage
function saveRTPValues(rtpValues) {
  localStorage.setItem("currentRTPValues", JSON.stringify(rtpValues));
}

// Filter games by provider (simulation)
function filterGamesByProvider(providerName) {
  const gameCards = document.querySelectorAll(".game-card");

  gameCards.forEach((card, index) => {
    card.style.opacity = "0.3";
    card.style.transform = "scale(0.95)";

    setTimeout(() => {
      card.style.opacity = "1";
      card.style.transform = "";
    }, index * 100 + 500);
  });
}

// Show notification
function showNotification(message, type = "info") {
  // Remove existing notifications
  const existingNotifications = document.querySelectorAll(".notification");
  existingNotifications.forEach((notif) => notif.remove());

  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${getNotificationIcon(type)}</span>
            <span class="notification-message">${message}</span>
        </div>
    `;

  // Add styles
  notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationBg(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        font-size: 14px;
    `;

  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.style.transform = "translateX(0)";
  }, 100);

  // Auto remove
  setTimeout(() => {
    notification.style.transform = "translateX(100%)";
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}

// Get notification icon
function getNotificationIcon(type) {
  const icons = {
    success: "✓",
    info: "ℹ",
    warning: "⚠",
    error: "✗",
  };
  return icons[type] || icons.info;
}

// Get notification background
function getNotificationBg(type) {
  const backgrounds = {
    success: "linear-gradient(45deg, #4CAF50, #66BB6A)",
    info: "linear-gradient(45deg, #2196F3, #64B5F6)",
    warning: "linear-gradient(45deg, #FF9800, #FFB74D)",
    error: "linear-gradient(45deg, #F44336, #EF5350)",
  };
  return backgrounds[type] || backgrounds.info;
}
