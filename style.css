* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #0f1419 100%);
  color: #ffffff;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Ticker Text */
.ticker-container {
  background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
  color: #000;
  padding: 8px 0;
  overflow: hidden;
  position: relative;
  border-bottom: 3px solid #ffd700;
}

.ticker-text {
  white-space: nowrap;
  animation: scroll-ticker 30s linear infinite;
  font-weight: 600;
  font-size: 14px;
}

@keyframes scroll-ticker {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Header */
.header {
  background: rgba(15, 20, 25, 0.95);
  padding: 15px 0;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo img {
  height: 50px;
  width: auto;
}

.header-buttons {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
  color: white;
}

.btn-secondary {
  background: linear-gradient(45deg, #ff9800, #ffb74d);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Main Banner */
.main-banner {
  text-align: center;
  padding: 20px 0;
  background: linear-gradient(
    45deg,
    rgba(15, 20, 25, 0.8),
    rgba(26, 35, 50, 0.8)
  );
}

.banner-img {
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* Main Content */
.main-content {
  padding: 40px 0;
}

.section-title {
  text-align: center;
  margin-bottom: 30px;
}

.section-title h2 {
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  color: #000;
  padding: 15px 30px;
  border-radius: 25px;
  display: inline-block;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.section-title .date {
  color: #ccc;
  font-size: 16px;
}

/* Providers Section */
.providers-section {
  margin-bottom: 40px;
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.provider-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border-radius: 10px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.provider-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.provider-item img {
  max-width: 100%;
  max-height: 60px;
  object-fit: contain;
/* Games Grid */
.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

/* Search and Filter Section */
.search-filter-section {
  background: rgba(26, 35, 50, 0.8);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-filter-section h3 {
  color: #ffd700;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.filter-row {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.filter-group input,
.filter-group select {
  padding: 10px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(15, 20, 25, 0.8);
  color: #ffffff;
  font-size: 14px;
  min-width: 200px;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.filter-group select option {
  background: #1a2332;
  color: #ffffff;
} max-width: 1400px;
  margin: 0 auto;
}

/* Game Card Styling */
.card.game-one-half-slot.slots-games {
  background: linear-gradient(145deg, #1a2332, #0f1419);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.card.game-one-half-slot.slots-games:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}



.game-image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.game-image-wrapper amp-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card.game-one-half-slot.slots-games:hover .game-image-wrapper amp-img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.card.game-one-half-slot.slots-games:hover .play-overlay {
  opacity: 1;
}

.play-btn {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.play-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

/* Progress Bar (RTP) */
.pbar {
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.rtp-text {
  color: #ffffff;
  font-weight: bold;
  font-size: 14px;
  min-width: 40px;
  text-align: right;
}

/* Pattern Section */
.my-icon {
  padding: 15px;
  background: rgba(15, 20, 25, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pattern-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pattern-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pattern-row:last-child {
  border-bottom: none;
}

.pattern-value {
  color: #ffd700;
  font-weight: bold;
  font-size: 14px;
  min-width: 30px;
}

.pattern-icons {
  display: flex;
  gap: 5px;
  flex: 1;
  justify-content: center;
}

.pattern-icons i.fa-check {
  color: #4ade80;
  font-size: 12px;
}

.pattern-icons i.fa-times {
  color: #ef4444;
  font-size: 12px;
}

.pattern-type {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
  text-align: right;
}

.pola-unavailable {
  color: #ef4444;
  font-size: 12px;
  text-align: center;
  padding: 10px;
  font-style: italic;
}

.game-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #f7931e, #ffd700);
  z-index: 1;
}

.game-card::after {
  content: "EKSLUSIF";
  position: absolute;
  top: 10px;
  left: 10px;
  background: linear-gradient(45deg, #8b5cf6, #a855f7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  z-index: 3;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.game-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.game-image {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.game-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.game-card:hover .game-image img {
  transform: scale(1.05);
}

.ekslusif-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: linear-gradient(45deg, #8b5cf6, #a855f7);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: bold;
  z-index: 3;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.rtp-progress-container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 8px;
  backdrop-filter: blur(10px);
  z-index: 2;
}

.rtp-progress-bar {
  position: relative;
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 6px;
}

.rtp-progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.rtp-progress-fill::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.rtp-percentage {
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.play-btn-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 4;
}

.game-image-container:hover .play-btn-overlay {
  opacity: 1;
}

/* Dynamic RTP Colors */
.rtp-progress-fill[data-rtp] {
  background: linear-gradient(45deg, #f44336, #ef5350);
}

.rtp-progress-fill[data-rtp="48"],
.rtp-progress-fill[data-rtp="49"],
.rtp-progress-fill[data-rtp="50"],
.rtp-progress-fill[data-rtp="51"],
.rtp-progress-fill[data-rtp="52"],
.rtp-progress-fill[data-rtp="53"],
.rtp-progress-fill[data-rtp="54"],
.rtp-progress-fill[data-rtp="55"],
.rtp-progress-fill[data-rtp="56"],
.rtp-progress-fill[data-rtp="57"],
.rtp-progress-fill[data-rtp="58"],
.rtp-progress-fill[data-rtp="59"],
.rtp-progress-fill[data-rtp="60"],
.rtp-progress-fill[data-rtp="61"],
.rtp-progress-fill[data-rtp="62"],
.rtp-progress-fill[data-rtp="63"],
.rtp-progress-fill[data-rtp="64"],
.rtp-progress-fill[data-rtp="65"],
.rtp-progress-fill[data-rtp="66"],
.rtp-progress-fill[data-rtp="67"],
.rtp-progress-fill[data-rtp="68"],
.rtp-progress-fill[data-rtp="69"] {
  background: linear-gradient(45deg, #f44336, #ef5350);
}

.rtp-progress-fill[data-rtp="70"],
.rtp-progress-fill[data-rtp="71"],
.rtp-progress-fill[data-rtp="72"],
.rtp-progress-fill[data-rtp="73"],
.rtp-progress-fill[data-rtp="74"],
.rtp-progress-fill[data-rtp="75"],
.rtp-progress-fill[data-rtp="76"],
.rtp-progress-fill[data-rtp="77"],
.rtp-progress-fill[data-rtp="78"],
.rtp-progress-fill[data-rtp="79"],
.rtp-progress-fill[data-rtp="80"],
.rtp-progress-fill[data-rtp="81"],
.rtp-progress-fill[data-rtp="82"],
.rtp-progress-fill[data-rtp="83"],
.rtp-progress-fill[data-rtp="84"] {
  background: linear-gradient(45deg, #ff9800, #ffb74d);
}

.rtp-progress-fill[data-rtp="85"],
.rtp-progress-fill[data-rtp="86"],
.rtp-progress-fill[data-rtp="87"],
.rtp-progress-fill[data-rtp="88"],
.rtp-progress-fill[data-rtp="89"],
.rtp-progress-fill[data-rtp="90"],
.rtp-progress-fill[data-rtp="91"],
.rtp-progress-fill[data-rtp="92"],
.rtp-progress-fill[data-rtp="93"],
.rtp-progress-fill[data-rtp="94"],
.rtp-progress-fill[data-rtp="95"],
.rtp-progress-fill[data-rtp="96"],
.rtp-progress-fill[data-rtp="97"],
.rtp-progress-fill[data-rtp="98"] {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
}

.play-btn {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.play-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
}

.game-info {
  padding: 15px;
  background: #16213e;
}

.pola-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
}

.pola-label {
  color: #ffd700;
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 8px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.pola-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pola-item {
  color: #e0e0e0;
  font-size: 11px;
  text-align: center;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
}

.pola-unavailable {
  color: #ff6b6b;
  font-size: 11px;
  text-align: center;
  padding: 8px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* Footer */
.footer {
  background: rgba(15, 20, 25, 0.95);
  padding: 20px 0;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 50px;
}

.footer p {
  color: #888;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .games-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .games-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    gap: 15px;
  }

  .header-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn {
    width: 100%;
  }

  .games-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .providers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
  }

  .section-title h2 {
    font-size: 20px;
    padding: 12px 24px;
  }

  .ticker-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .games-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .providers-grid {
    grid-template-columns: 1fr;
  }

  .game-image {
    height: 140px;
  }

  .section-title h2 {
    font-size: 18px;
    padding: 10px 20px;
  }
}

/* Loading Animation */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Scroll Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-card {
  animation: fadeInUp 0.6s ease-out;
}

.game-card:nth-child(odd) {
  animation-delay: 0.1s;
}

.game-card:nth-child(even) {
  animation-delay: 0.2s;
}
