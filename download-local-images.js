const https = require("https");
const http = require("http");
const fs = require("fs");
const path = require("path");
const { URL } = require("url");

console.log("Starting local image extraction...");

const imagesDir = "./images";
const indexHtmlPath = "./index.html";

// Create images directory if it doesn't exist
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to download image
function downloadImage(imageUrl, filename) {
  return new Promise((resolve, reject) => {
    const protocol = imageUrl.startsWith("https:") ? https : http;

    protocol
      .get(imageUrl, (response) => {
        if (response.statusCode === 200) {
          const filePath = path.join(imagesDir, filename);
          const fileStream = fs.createWriteStream(filePath);

          response.pipe(fileStream);

          fileStream.on("finish", () => {
            fileStream.close();
            console.log(`Downloaded: ${filename}`);
            resolve(filePath);
          });

          fileStream.on("error", (err) => {
            fs.unlink(filePath, () => {});
            reject(err);
          });
        } else {
          reject(
            new Error(`Failed to download ${imageUrl}: ${response.statusCode}`)
          );
        }
      })
      .on("error", reject);
  });
}

// Function to extract images from local HTML file
function extractImagesFromLocalHtml() {
  try {
    const html = fs.readFileSync(indexHtmlPath, "utf8");
    const imageUrls = new Set();

    // Regular expressions to find image URLs
    const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
    const backgroundRegex = /background-image:\s*url\(["']?([^"')]+)["']?\)/gi;

    let match;

    // Extract img src
    while ((match = imgRegex.exec(html)) !== null) {
      imageUrls.add(match[1]);
    }

    // Extract background images
    while ((match = backgroundRegex.exec(html)) !== null) {
      imageUrls.add(match[1]);
    }

    // Filter only external URLs (http/https)
    const externalUrls = Array.from(imageUrls).filter(
      (url) => url.startsWith("http://") || url.startsWith("https://")
    );

    return externalUrls;
  } catch (error) {
    console.error("Error reading HTML file:", error);
    return [];
  }
}

// Function to get filename from URL
function getFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    let filename = path.basename(pathname);

    if (!filename || !path.extname(filename)) {
      filename = "image_" + Date.now() + ".jpg";
    }

    return filename;
  } catch (error) {
    return "image_" + Date.now() + ".jpg";
  }
}

// Function to create mapping for HTML replacement
function createImageMapping(results) {
  const mapping = {};
  results.forEach((result) => {
    if (result.success) {
      mapping[result.url] = `./images/${result.filename}`;
    }
  });
  return mapping;
}

// Function to update HTML with local image paths
function updateHtmlWithLocalImages(mapping) {
  try {
    let html = fs.readFileSync(indexHtmlPath, "utf8");

    // Replace image URLs with local paths
    Object.keys(mapping).forEach((originalUrl) => {
      const localPath = mapping[originalUrl];
      const regex = new RegExp(
        originalUrl.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
        "g"
      );
      html = html.replace(regex, localPath);
    });

    // Save updated HTML
    const updatedHtmlPath = "./index-local.html";
    fs.writeFileSync(updatedHtmlPath, html);
    console.log(`Updated HTML saved to: ${updatedHtmlPath}`);

    return updatedHtmlPath;
  } catch (error) {
    console.error("Error updating HTML:", error);
    return null;
  }
}

// Main function
async function main() {
  console.log("Extracting images from local HTML file...");

  // Extract image URLs from local HTML
  const imageUrls = extractImagesFromLocalHtml();
  console.log(`Found ${imageUrls.length} external images`);

  if (imageUrls.length === 0) {
    console.log("No external images found to download.");
    return;
  }

  // Download images
  const downloadPromises = imageUrls.map(async (url, index) => {
    try {
      const filename = getFilenameFromUrl(url);
      const uniqueFilename = `${index + 1}_${filename}`;
      await downloadImage(url, uniqueFilename);
      return { url, filename: uniqueFilename, success: true };
    } catch (error) {
      console.error(`Failed to download ${url}:`, error.message);
      return { url, filename: null, success: false, error: error.message };
    }
  });

  const results = await Promise.all(downloadPromises);

  // Summary
  const successful = results.filter((r) => r.success).length;
  const failed = results.filter((r) => !r.success).length;

  console.log(`\nDownload Summary:`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${failed}`);
  console.log(`Total: ${results.length}`);

  // Save results to JSON file
  fs.writeFileSync(
    "./local-download-results.json",
    JSON.stringify(results, null, 2)
  );
  console.log("Results saved to local-download-results.json");

  // Create image mapping and update HTML
  if (successful > 0) {
    const mapping = createImageMapping(results);
    const updatedHtmlPath = updateHtmlWithLocalImages(mapping);

    if (updatedHtmlPath) {
      console.log("\nHTML file updated with local image paths!");
      console.log(
        "You can now open index-local.html to view the cloned website."
      );
    }
  }
}

// Start the process
main().catch(console.error);
