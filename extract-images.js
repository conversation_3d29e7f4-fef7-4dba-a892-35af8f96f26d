const https = require("https");
const http = require("http");
const fs = require("fs");
const path = require("path");
const { URL } = require("url");

console.log("Script starting...");

// Target website URL
const targetUrl = "https://rtpslotgeng414.com/";
const imagesDir = "./images";
const indexHtmlPath = "./index.html";

// Create images directory if it doesn't exist
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to download image
function downloadImage(imageUrl, filename) {
  return new Promise((resolve, reject) => {
    const protocol = imageUrl.startsWith("https:") ? https : http;

    protocol
      .get(imageUrl, (response) => {
        if (response.statusCode === 200) {
          const filePath = path.join(imagesDir, filename);
          const fileStream = fs.createWriteStream(filePath);

          response.pipe(fileStream);

          fileStream.on("finish", () => {
            fileStream.close();
            console.log(`Downloaded: ${filename}`);
            resolve(filePath);
          });

          fileStream.on("error", (err) => {
            fs.unlink(filePath, () => {});
            reject(err);
          });
        } else {
          reject(
            new Error(`Failed to download ${imageUrl}: ${response.statusCode}`)
          );
        }
      })
      .on("error", reject);
  });
}

// Function to extract images from HTML
function extractImagesFromHtml(html, baseUrl) {
  const imageUrls = new Set();

  // Regular expressions to find image URLs
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
  const backgroundRegex = /background-image:\s*url\(["']?([^"')]+)["']?\)/gi;
  const ampImgRegex = /<amp-img[^>]+src=["']([^"']+)["'][^>]*>/gi;

  let match;

  // Extract img src
  while ((match = imgRegex.exec(html)) !== null) {
    imageUrls.add(match[1]);
  }

  // Extract background images
  while ((match = backgroundRegex.exec(html)) !== null) {
    imageUrls.add(match[1]);
  }

  // Extract amp-img src
  while ((match = ampImgRegex.exec(html)) !== null) {
    imageUrls.add(match[1]);
  }

  // Convert relative URLs to absolute
  const absoluteUrls = Array.from(imageUrls).map((url) => {
    if (url.startsWith("http")) {
      return url;
    } else if (url.startsWith("//")) {
      return "https:" + url;
    } else if (url.startsWith("/")) {
      return baseUrl + url;
    } else {
      return baseUrl + "/" + url;
    }
  });

  return absoluteUrls;
}

// Function to get filename from URL
function getFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    let filename = path.basename(pathname);

    if (!filename || !path.extname(filename)) {
      filename = "image_" + Date.now() + ".jpg";
    }

    return filename;
  } catch (error) {
    return "image_" + Date.now() + ".jpg";
  }
}

// Main function to scrape website
function scrapeWebsite() {
  console.log(`Scraping website: ${targetUrl}`);

  https
    .get(targetUrl, (response) => {
      let html = "";

      response.on("data", (chunk) => {
        html += chunk;
      });

      response.on("end", async () => {
        console.log("HTML content received");

        // Extract image URLs
        const imageUrls = extractImagesFromHtml(
          html,
          "https://rtpslotgeng414.com"
        );
        console.log(`Found ${imageUrls.length} images`);

        // Download images
        const downloadPromises = imageUrls.map(async (url, index) => {
          try {
            const filename = getFilenameFromUrl(url);
            const uniqueFilename = `${index + 1}_${filename}`;
            await downloadImage(url, uniqueFilename);
            return { url, filename: uniqueFilename, success: true };
          } catch (error) {
            console.error(`Failed to download ${url}:`, error.message);
            return {
              url,
              filename: null,
              success: false,
              error: error.message,
            };
          }
        });

        const results = await Promise.all(downloadPromises);

        // Summary
        const successful = results.filter((r) => r.success).length;
        const failed = results.filter((r) => !r.success).length;

        console.log(`\nDownload Summary:`);
        console.log(`Successful: ${successful}`);
        console.log(`Failed: ${failed}`);
        console.log(`Total: ${results.length}`);

        // Save results to JSON file
        fs.writeFileSync(
          "./download-results.json",
          JSON.stringify(results, null, 2)
        );
        console.log("Results saved to download-results.json");
      });
    })
    .on("error", (error) => {
      console.error("Error scraping website:", error);
    });
}

// Start scraping
scrapeWebsite();
