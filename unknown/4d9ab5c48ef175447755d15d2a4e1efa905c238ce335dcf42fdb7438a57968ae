import{r,j as e,T as z,D as I,M as k,a as A,W as O,b as F,c as V,d as $}from"./assets/defaultSettingsView-Do_wwdKw.js";const M=({className:n,style:o,open:s,isModal:a,width:l,verticalOffset:m,requestClose:u,anchor:x,dataTestId:p,children:T})=>{const g=r.useRef(null),[E,b]=r.useState(0);let j=o;if(x!=null&&x.current){const h=x.current.getBoundingClientRect();j={position:"fixed",margin:0,top:h.bottom+(m??0),left:G(h,l??0),width:l,zIndex:1,...o}}return r.useEffect(()=>{const h=y=>{!g.current||!(y.target instanceof Node)||g.current.contains(y.target)||u==null||u()},L=y=>{y.key==="Escape"&&(u==null||u())};return s?(document.addEventListener("mousedown",h),document.addEventListener("keydown",L),()=>{document.removeEventListener("mousedown",h),document.removeEventListener("keydown",L)}):()=>{}},[s,u]),r.useEffect(()=>{const h=()=>b(L=>L+1);return window.addEventListener("resize",h),()=>{window.removeEventListener("resize",h)}},[]),r.useLayoutEffect(()=>{g.current&&(s?a?g.current.showModal():g.current.show():g.current.close())},[s,a]),e.jsx("dialog",{ref:g,style:j,className:n,"data-testid":p,children:T})},G=(n,o)=>{const s=U(n,o,"left");if(s.inBounds)return s.value;const a=U(n,o,"right");return a.inBounds?a.value:s.value},U=(n,o,s)=>{const a=document.documentElement.clientWidth;if(s==="left"){const l=n.left;return{value:l,inBounds:l+o<=a}}else return{value:n.right-o,inBounds:n.right-o>=0}},H=()=>{const n=r.useRef(null),[o,s]=r.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(z,{ref:n,icon:"settings-gear",title:"Settings",onClick:()=>s(a=>!a)}),e.jsx(M,{style:{backgroundColor:"var(--vscode-sideBar-background)",padding:"4px 8px"},open:o,width:200,verticalOffset:8,requestClose:()=>s(!1),anchor:n,dataTestId:"settings-toolbar-dialog",children:e.jsx(I,{})})]})},K=()=>{const[n,o]=r.useState(!1),[s,a]=r.useState([]),[l,m]=r.useState([]),[u,x]=r.useState(W),[p,T]=r.useState({done:0,total:0}),[g,E]=r.useState(!1),[b,j]=r.useState(null),[h,L]=r.useState(null),[y,N]=r.useState(!1),S=r.useCallback(t=>{const c=[],d=[],i=new URL(window.location.href);for(let f=0;f<t.length;f++){const w=t.item(f);if(!w)continue;const P=URL.createObjectURL(w);c.push(P),d.push(w.name),i.searchParams.append("trace",P),i.searchParams.append("traceFileName",w.name)}const v=i.toString();window.history.pushState({},"",v),a(c),m(d),E(!1),j(null)},[]);r.useEffect(()=>{const t=async c=>{var d;if((d=c.clipboardData)!=null&&d.files.length){for(const i of c.clipboardData.files)if(i.type!=="application/zip")return;c.preventDefault(),S(c.clipboardData.files)}};return document.addEventListener("paste",t),()=>document.removeEventListener("paste",t)}),r.useEffect(()=>{const t=c=>{const{method:d,params:i}=c.data;if(d!=="load"||!((i==null?void 0:i.trace)instanceof Blob))return;const v=new File([i.trace],"trace.zip",{type:"application/zip"}),f=new DataTransfer;f.items.add(v),S(f.files)};return window.addEventListener("message",t),()=>window.removeEventListener("message",t)});const B=r.useCallback(t=>{t.preventDefault(),S(t.dataTransfer.files)},[S]),C=r.useCallback(t=>{t.preventDefault(),t.target.files&&S(t.target.files)},[S]);r.useEffect(()=>{const t=new URL(window.location.href).searchParams,c=t.getAll("trace");o(t.has("isServer"));for(const d of c)if(d.startsWith("file:")){L(d||null);return}if(t.has("isServer")){const d=new URLSearchParams(window.location.search).get("ws"),i=new URL(`../${d}`,window.location.toString());i.protocol=window.location.protocol==="https:"?"wss:":"ws:";const v=new A(new O(i));v.onLoadTraceRequested(async f=>{a(f.traceUrl?[f.traceUrl]:[]),E(!1),j(null)}),v.initialize({}).catch(()=>{})}else c.some(d=>d.startsWith("blob:"))||a(c)},[]),r.useEffect(()=>{(async()=>{if(s.length){const t=i=>{i.data.method==="progress"&&T(i.data.params)};navigator.serviceWorker.addEventListener("message",t),T({done:0,total:1});const c=[];for(let i=0;i<s.length;i++){const v=s[i],f=new URLSearchParams;f.set("trace",v),l.length&&f.set("traceFileName",l[i]),f.set("limit",String(s.length));const w=await fetch(`contexts?${f.toString()}`);if(!w.ok){n||a([]),j((await w.json()).error);return}c.push(...await w.json())}navigator.serviceWorker.removeEventListener("message",t);const d=new k(c);T({done:0,total:0}),x(d)}else x(W)})()},[n,s,l]);const D=p.done!==p.total&&p.total!==0;r.useEffect(()=>{if(D){const t=setTimeout(()=>{N(!0)},200);return()=>clearTimeout(t)}else N(!1)},[D]);const R=!!(!n&&!g&&!h&&(!s.length||b));return e.jsxs("div",{className:"vbox workbench-loader",onDragOver:t=>{t.preventDefault(),E(!0)},children:[e.jsxs("div",{className:"hbox header",...R?{inert:"true"}:{},children:[e.jsx("div",{className:"logo",children:e.jsx("img",{src:"playwright-logo.svg",alt:"Playwright logo"})}),e.jsx("div",{className:"product",children:"Playwright"}),u.title&&e.jsx("div",{className:"title",children:u.title}),e.jsx("div",{className:"spacer"}),e.jsx(H,{})]}),e.jsx(F,{model:u,inert:R}),h&&e.jsxs("div",{className:"drop-target",children:[e.jsx("div",{children:"Trace Viewer uses Service Workers to show traces. To view trace:"}),e.jsxs("div",{style:{paddingTop:20},children:[e.jsxs("div",{children:["1. Click ",e.jsx("a",{href:h,children:"here"})," to put your trace into the download shelf"]}),e.jsxs("div",{children:["2. Go to ",e.jsx("a",{href:"https://trace.playwright.dev",children:"trace.playwright.dev"})]}),e.jsx("div",{children:"3. Drop the trace from the download shelf into the page"})]})]}),e.jsx(M,{open:y,isModal:!0,className:"progress-dialog",children:e.jsxs("div",{className:"progress-content",children:[e.jsx("div",{className:"title",role:"heading","aria-level":1,children:"Loading Playwright Trace..."}),e.jsx("div",{className:"progress-wrapper",children:e.jsx("div",{className:"inner-progress",style:{width:p.total?100*p.done/p.total+"%":0}})})]})}),R&&e.jsxs("div",{className:"drop-target",children:[e.jsx("div",{className:"processing-error",role:"alert",children:b}),e.jsx("div",{className:"title",role:"heading","aria-level":1,children:"Drop Playwright Trace to load"}),e.jsx("div",{children:"or"}),e.jsx("button",{onClick:()=>{const t=document.createElement("input");t.type="file",t.multiple=!0,t.click(),t.addEventListener("change",c=>C(c))},type:"button",children:"Select file(s)"}),e.jsx("div",{style:{maxWidth:400},children:"Playwright Trace Viewer is a Progressive Web App, it does not send your trace anywhere, it opens it locally."})]}),n&&!s.length&&e.jsx("div",{className:"drop-target",children:e.jsx("div",{className:"title",children:"Select test to see the trace"})}),g&&e.jsx("div",{className:"drop-target",onDragLeave:()=>{E(!1)},onDrop:t=>B(t),children:e.jsx("div",{className:"title",children:"Release to analyse the Playwright Trace"})})]})},W=new k([]),_=({traceJson:n})=>{const[o,s]=r.useState(void 0),[a,l]=r.useState(0),m=r.useRef(null);return r.useEffect(()=>(m.current&&clearTimeout(m.current),m.current=setTimeout(async()=>{try{const u=await J(n);s(u)}catch{const u=new k([]);s(u)}finally{l(a+1)}},500),()=>{m.current&&clearTimeout(m.current)}),[n,a]),e.jsx(F,{model:o,isLive:!0})};async function J(n){const o=new URLSearchParams;o.set("trace",n),o.set("limit","1");const a=await(await fetch(`contexts?${o.toString()}`)).json();return new k(a)}(async()=>{const n=new URLSearchParams(window.location.search);if(V(),window.location.protocol!=="file:"){if(n.get("isUnderTest")==="true"&&await new Promise(l=>setTimeout(l,1e3)),!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the Trace Viewer (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(l=>{navigator.serviceWorker.oncontrollerchange=()=>l()}),setInterval(function(){fetch("ping")},1e4)}const o=n.get("trace"),a=(o==null?void 0:o.endsWith(".json"))?e.jsx(_,{traceJson:o}):e.jsx(K,{});$.createRoot(document.querySelector("#root")).render(a)})();
